// =============================================================================
// REVISA.AI DATABASE SEEDING SCRIPT (OAuth Update)
// =============================================================================
// This script seeds the development database with passwordless sample data
// Run with: npx prisma db seed

import { PrismaClient, UserTier, MessageRole } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create admin user (passwordless)
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Admin User',
      // picture and googleId will be populated on first login
      tier: UserTier.ENTERPRISE,
      apiQuota: 10000000,
    },
  });

  console.log('✅ Created admin user:', adminUser.email);

  // Create test user (passwordless)
  const testUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Test User',
      tier: UserTier.FREE,
      apiQuota: 100000,
    },
  });

  console.log('✅ Created test user:', testUser.email);

  // Create sample thread for admin user
  const sampleThread = await prisma.thread.create({
    data: {
      userId: adminUser.id,
      title: 'Welcome to Revisa AI',
    },
  });

  console.log('✅ Created sample thread:', sampleThread.title);

  // Create sample messages
  await prisma.message.createMany({
    data: [
      {
        threadId: sampleThread.id,
        role: MessageRole.USER,
        content: 'Hello! How can you help me today?',
        tokens: 12,
        model: 'gemini-2.5-flash-lite',
      },
      {
        threadId: sampleThread.id,
        role: MessageRole.ASSISTANT,
        content:
          "Hello! I'm Revisa AI, your intelligent assistant. I can help you with a wide variety of tasks including answering questions, writing, analysis, coding, and much more. What would you like to work on today?",
        tokens: 45,
        model: 'gemini-2.5-flash-lite',
      },
    ],
  });

  console.log('✅ Created sample messages');

  // Create sample API usage records
  await prisma.apiUsage.createMany({
    data: [
      {
        userId: adminUser.id,
        provider: 'openai',
        model: 'gpt-4o-mini',
        tokens: 57,
        cost: 0.000057,
      },
      {
        userId: testUser.id,
        provider: 'openai',
        model: 'gpt-4o-mini',
        tokens: 25,
        cost: 0.000025,
      },
    ],
  });

  console.log('✅ Created sample API usage records');

  // Create system configuration
  await prisma.systemConfig.upsert({
    where: { key: 'app_version' },
    update: { value: '1.0.0' },
    create: {
      key: 'app_version',
      value: '1.0.0',
    },
  });

  await prisma.systemConfig.upsert({
    where: { key: 'maintenance_mode' },
    update: { value: 'false' },
    create: {
      key: 'maintenance_mode',
      value: 'false',
    },
  });

  console.log('✅ Created system configuration');

  console.log('🎉 Database seeding completed successfully!');
  console.log('');
  console.log('📋 Sample accounts created:');
  console.log('  - <EMAIL>');
  console.log('  - <EMAIL>');
  console.log('  (Log in with these accounts via the Google login flow)');
  console.log('');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
