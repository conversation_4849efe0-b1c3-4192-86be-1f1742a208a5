// =============================================================================
// REVISA.AI PRISMA SCHEMA
// =============================================================================
// This schema defines the database structure for the ChatGPT-style application
// It's designed to be deploy-agnostic and work across different environments

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// =============================================================================
// USER MANAGEMENT
// =============================================================================

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String
  password  String? // Hashed password, optional for OAuth users
  tier      UserTier @default(FREE)
  apiQuota  Int      @default(100000) @map("api_quota")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  googleId  String?  @unique @map("google_id")
  picture   String?

  // Relations
  threads      Thread[]
  apiUsage     ApiUsage[]
  sessions     Session[]
  RefreshToken RefreshToken[]

  @@map("users")
}

enum UserTier {
  FREE
  PRO
  ENTERPRISE

  @@map("user_tier")
}

// =============================================================================
// AUTHENTICATION & SESSIONS
// =============================================================================

model Session {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  token     String   @unique
  expiresAt DateTime @map("expires_at")
  createdAt DateTime @default(now()) @map("created_at")

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

// =============================================================================
// CHAT SYSTEM
// =============================================================================

model Thread {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  title     String
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  user     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  messages Message[]

  @@index([userId])
  @@index([createdAt(sort: Desc)])
  @@map("threads")
}

model Message {
  id        String      @id @default(cuid())
  threadId  String      @map("thread_id")
  role      MessageRole
  content   String
  tokens    Int         @default(0)
  model     String?
  createdAt DateTime    @default(now()) @map("created_at")
  deletedAt DateTime?   @map("deleted_at")

  idempotencyKey String? @map("idempotency_key")

  // Tree structure fields
  parentId        String? @map("parent_id")
  nextMessageId   String? @unique @map("next_message_id") // The ID of the next message in the active path
  siblingPosition Int     @default(1) @map("sibling_position") // 1-based index for ordering siblings

  // Relations
  thread      Thread    @relation(fields: [threadId], references: [id], onDelete: Cascade)
  parent      Message?  @relation("MessageChildren", fields: [parentId], references: [id], onDelete: NoAction)
  children    Message[] @relation("MessageChildren")
  nextMessage Message?  @relation("MessageNext", fields: [nextMessageId], references: [id], onDelete: NoAction)
  prevMessage Message?  @relation("MessageNext")

  @@unique([idempotencyKey])
  @@index([threadId])
  @@index([parentId])
  @@index([deletedAt])
  @@map("messages")
}

enum MessageRole {
  USER
  ASSISTANT
  SYSTEM

  @@map("message_role")
}

// =============================================================================
// API USAGE & BILLING
// =============================================================================

model ApiUsage {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  provider  String // openai, openrouter, azure, etc.
  model     String // gpt-4o, claude-3, etc.
  tokens    Int
  cost      Decimal  @default(0) @db.Decimal(10, 6)
  createdAt DateTime @default(now()) @map("created_at")

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([createdAt])
  @@map("api_usage")
}

// =============================================================================
// SYSTEM CONFIGURATION
// =============================================================================

model SystemConfig {
  id    String @id @default(cuid())
  key   String @unique
  value String

  @@map("system_config")
}

// prisma/schema.prisma (add this model)
model RefreshToken {
  id        String   @id @default(cuid())
  tokenId   String   @unique @map("token_id")
  familyId  String   @map("family_id")
  userId    String   @map("user_id")
  expiresAt DateTime @map("expires_at")
  isRevoked Boolean  @default(false) @map("is_revoked")
  createdAt DateTime @default(now()) @map("created_at")

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([familyId])
  @@index([tokenId])
  @@index([expiresAt])
  @@map("refresh_tokens")
}
