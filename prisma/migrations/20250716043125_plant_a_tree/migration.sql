/*
  Warnings:

  - A unique constraint covering the columns `[next_message_id]` on the table `messages` will be added. If there are existing duplicate values, this will fail.

*/
-- DropIndex
DROP INDEX "messages_created_at_idx";

-- AlterTable
ALTER TABLE "messages" ADD COLUMN     "deleted_at" TIMESTAMP(3),
ADD COLUMN     "next_message_id" TEXT,
ADD COLUMN     "parent_id" TEXT,
ADD COLUMN     "sibling_position" INTEGER NOT NULL DEFAULT 1;

-- CreateIndex
CREATE UNIQUE INDEX "messages_next_message_id_key" ON "messages"("next_message_id");

-- CreateIndex
CREATE INDEX "messages_parent_id_idx" ON "messages"("parent_id");

-- CreateIndex
CREATE INDEX "messages_deleted_at_idx" ON "messages"("deleted_at");

-- AddForeignKey
ALTER TABLE "messages" ADD CONSTRAINT "messages_parent_id_fkey" FOREIGN KEY ("parent_id") REFERENCES "messages"("id") ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeign<PERSON>ey
ALTER TABLE "messages" ADD CONSTRAINT "messages_next_message_id_fkey" FOREIGN KEY ("next_message_id") REFERENCES "messages"("id") ON DELETE NO ACTION ON UPDATE CASCADE;
