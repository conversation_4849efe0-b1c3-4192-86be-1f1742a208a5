// eslint.config.mjs
import { config as nestjsConfig } from './packages/eslint-config/nestjs.js';
import { config as reactConfig } from './packages/eslint-config/react.js';
import { config as baseConfig } from './packages/eslint-config/base.js';

/**
 * @type {import('eslint').Linter.Config[]}
 */
export default [
  // Apply base configuration to all files
  ...baseConfig,

  // Apply NestJS-specific configuration to the server app
  {
    files: ['apps/server/**/*.ts'],
    ...nestjsConfig.find(c => c.files?.includes('**/*.ts')), // Extract the rules object
  },

  // Apply React-specific configuration to the www app
  {
    files: ['apps/www/**/*.{js,jsx,ts,tsx}'],
    ...reactConfig.find(c => c.files?.includes('**/*.{js,jsx,ts,tsx}')), // Extract the rules object
  },

  // Global ignores
  {
    ignores: [
      '**/dist/**',
      '**/build/**',
      '**/node_modules/**',
      '**/.turbo/**',
      '**/.react-router/**',
      'coverage/**',
    ],
  },
];