# Development Environment - Local Database Containers
# Industry Best Practice: Use containers for dev, managed services for prod

services:
  # PostgreSQL Database (Development Only)
  postgres:
    image: postgres:16-alpine
    container_name: revisa-postgres-dev
    environment:
      POSTGRES_DB: revisa_dev
      POSTGRES_USER: revisa_user
      POSTGRES_PASSWORD: revisa_password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - '5432:5432'
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    networks:
      - revisa-dev-network
    restart: unless-stopped
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U revisa_user -d revisa_dev']
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache/Queue (Development Only)
  redis:
    image: redis:7-alpine
    container_name: revisa-redis-dev
    command: redis-server --requirepass revisa_redis_password
    ports:
      - '6379:6379'
    volumes:
      - redis_dev_data:/data
    networks:
      - revisa-dev-network
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'redis-cli', '-a', 'revisa_redis_password', 'ping']
      interval: 10s
      timeout: 5s
      retries: 5

  # NestJS Server (Development)
  server:
    build:
      context: .
      dockerfile: apps/server/Dockerfile.dev
    container_name: revisa-server-dev
    ports:
      - '3001:3000'
      - '9229:9229' # Debug port
    environment:
      - NODE_ENV=development
      - PORT=3000
      - DATABASE_URL=******************************************************/revisa_dev
      - REDIS_URL=redis://:revisa_redis_password@redis:6379
    env_file:
      - ./apps/server/.env
    volumes:
      - ./apps/server:/app/apps/server
      - ./packages:/app/packages
    networks:
      - revisa-dev-network
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy

  # React Router Frontend (Development)
  www:
    build:
      context: .
      dockerfile: apps/www/Dockerfile.dev
    container_name: revisa-www-dev
    ports:
      - '3000:3000'
    environment:
      - NODE_ENV=development
      - PORT=3000
      - VITE_API_URL=http://localhost:3001
    env_file:
      - ./apps/www/.env
    volumes:
      - ./apps/www:/app/apps/www
      - ./packages:/app/packages
    networks:
      - revisa-dev-network
    restart: unless-stopped
    depends_on:
      - server

networks:
  revisa-dev-network:
    driver: bridge
    name: revisa-dev-network

volumes:
  postgres_dev_data:
    name: revisa-postgres-dev-data
  redis_dev_data:
    name: revisa-redis-dev-data
