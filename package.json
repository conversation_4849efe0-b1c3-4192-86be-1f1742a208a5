{"name": "revisa-ai", "private": true, "packageManager": "npm@10.9.2", "workspaces": ["apps/*", "packages/*"], "devDependencies": {"@types/bcrypt": "^5.0.2", "dotenv-cli": "^8.0.0", "prettier": "^3.4.2", "prettier-plugin-prisma": "^5.0.0", "prettier-plugin-sh": "^0.18.0", "prisma": "^6.11.1", "tsx": "^4.20.3", "turbo": "^2.5.4", "typescript": "^5.8.2"}, "scripts": {"dev": "turbo run dev start:dev --parallel", "dev:server": "npm --prefix apps/server run start:dev", "dev:www": "npm --prefix apps/www run dev", "build": "turbo run build", "start": "cd apps/server && node dist/main.js", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,md,json}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,md,json}\"", "test": "turbo run test", "typecheck": "turbo run typecheck", "setup:dev": "./scripts/dev-setup.sh", "db:up": "docker compose -f docker-compose.dev.yml up -d postgres redis", "db:down": "docker compose -f docker-compose.dev.yml down", "db:logs": "docker compose -f docker-compose.dev.yml logs -f postgres redis", "db:migrate": "dotenv -e apps/server/.env -- npx prisma migrate dev", "db:reset": "dotenv -e apps/server/.env -- npx prisma migrate reset", "db:seed": "dotenv -e apps/server/.env -- npx prisma db seed", "db:studio": "dotenv -e apps/server/.env -- npx prisma studio", "docker:dev": "docker compose -f docker-compose.dev.yml up", "docker:dev:build": "docker compose -f docker-compose.dev.yml up --build"}, "overrides": {"eslint": "9.30.0"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@fontsource/geist-mono": "^5.2.6", "@fontsource/geist-sans": "^5.2.5", "@prisma/client": "^6.11.1", "bcrypt": "^6.0.0"}, "lint-staged": {"apps/**/*.{ts,tsx,js,jsx}": ["eslint --fix"], "**/*.{ts,tsx,js,jsx,json,md,sh,yml,prisma}": ["prettier --write"]}}