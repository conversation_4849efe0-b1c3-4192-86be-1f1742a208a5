{
  email <EMAIL>
}

:{$PORT} {
  encode gzip
	log

  header {
    Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
    X-Content-Type-Options "nosniff"
    X-Frame-Options "DENY"
  }
  
	# Route requests for /api/* to the backend service.
	# The 'handle' directive does NOT strip the path.
	handle /api/* {
		reverse_proxy {$SERVER_PRIVATE_URL}
	}

	# Handle all other requests by sending them to the frontend service.
	handle {
		reverse_proxy {$WWW_PRIVATE_URL}
	}
}

:80 {
  tls internal
  respond "OK" 200
}