import { <PERSON>, Get, Param, <PERSON><PERSON>, Sse } from '@nestjs/common';
import { Response } from 'express';
import { Observable, interval, map, take } from 'rxjs';
import { AppService } from './app.service';
import { Throttle, seconds } from '@nestjs/throttler';
import { UserParamDto } from './dto/user-param.dto';
import { UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  @Get('api/test')
  getTestData() {
    return {
      message: `Hello from Revisa server!`,
      timestamp: new Date().toISOString(),
      server: 'NestJS',
      status: 'success',
    };
  }

  @Get('api/user/:id')
  getUserData(@Param() params: UserParamDto, @Res() res: Response) {
    const id = params.id;
    const userData = {
      id: id || Math.floor(Math.random() * 1000),
      name: 'Test User',
      email: '<EMAIL>',
      createdAt: new Date().toISOString(),
      preferences: {
        theme: 'dark',
        notifications: true,
      },
    };

    // Simulate some processing time
    setTimeout(() => {
      res.json(userData);
    }, 500);
  }

  @UseGuards(AuthGuard('jwt'))
  @Sse('api/stream/typing')
  @Throttle({ default: { limit: 1, ttl: seconds(10) } })
  streamTypingText(): Observable<{
    data: {
      char: string;
      index: number;
      isComplete: boolean;
      timestamp: string;
    };
  }> {
    const text =
      'Hi yah! This is a streaming text animation from the Revisa server. Each character appears one by one, simulating a typing effect. This demonstrates Server-Sent Events (SSE) working between your React client and NestJS server deployed on Railway! 🚀';

    return interval(5).pipe(
      take(text.length),
      map((index) => ({
        data: {
          char: text[index],
          index,
          isComplete: index === text.length - 1,
          timestamp: new Date().toISOString(),
        },
      })),
    );
  }

  @UseGuards(AuthGuard('jwt'))
  @Sse('api/stream/progress')
  @Throttle({ default: { limit: 1, ttl: seconds(10) } })
  streamProgress(): Observable<{
    data: {
      progress: number;
      message: string;
      isComplete: boolean;
      timestamp: string;
    };
  }> {
    return interval(10).pipe(
      take(101),
      map((value) => ({
        data: {
          progress: value,
          message: `Processing... ${value}%`,
          isComplete: value === 100,
          timestamp: new Date().toISOString(),
        },
      })),
    );
  }
}
