import { Injectable } from '@nestjs/common';
import {
  HealthIndicatorService,
  HealthIndicatorResult,
} from '@nestjs/terminus';
import { RedisService } from '../redis/redis.service';

@Injectable()
export class RedisHealthIndicator {
  constructor(
    private healthService: HealthIndicatorService,
    private redisService: RedisService,
  ) {}

  async isHealthy(key: string): Promise<HealthIndicatorResult> {
    const indicator = this.healthService.check(key);
    try {
      await this.redisService.getClient().ping();
      return indicator.up();
    } catch {
      return indicator.down({ message: 'Redis unreachable' });
    }
  }
}
