import { Module } from '@nestjs/common';
import { TerminusModule, HealthIndicatorService } from '@nestjs/terminus';
import { PrismaHealthIndicator } from './prisma.health';
import { RedisHealthIndicator } from './redis.health';
import { HealthController } from './health.controller';
import { PrismaModule } from '../prisma/prisma.module';
import { RedisModule } from 'src/redis/redis.module';

@Module({
  imports: [TerminusModule, PrismaModule, RedisModule],
  providers: [
    HealthIndicatorService,
    PrismaHealthIndicator,
    RedisHealthIndicator,
  ],
  controllers: [HealthController],
})
export class HealthModule {}
