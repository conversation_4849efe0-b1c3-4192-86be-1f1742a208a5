// apps/server/src/auth/strategies/refresh-token.strategy.ts
import { Injectable, UnauthorizedException, Logger } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-jwt';
import { Request } from 'express';
import { PrismaService } from '../../prisma/prisma.service';
import { RefreshTokenService } from '../refresh-token.service';

const refreshTokenExtractor = (req: Request): string | null => {
  if (req && req.cookies) {
    return req.cookies['refresh_token'];
  }
  return null;
};

@Injectable()
export class RefreshTokenStrategy extends PassportStrategy(
  Strategy,
  'jwt-refresh',
) {
  // Add a logger instance for this specific strategy
  private readonly logger = new Logger(RefreshTokenStrategy.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly refreshTokenService: RefreshTokenService,
  ) {
    super({
      jwtFromRequest: refreshTokenExtractor,
      ignoreExpiration: false,
      secretOrKey: process.env.JWT_REFRESH_SECRET,
    });
  }

  async validate(payload: {
    tokenId: string;
    sub: string;
    email: string;
    name: string;
    familyId: string;
  }) {
    const storedToken = await this.prisma.refreshToken.findUnique({
      where: { tokenId: payload.tokenId },
    });

    if (!storedToken || storedToken.expiresAt < new Date()) {
      throw new UnauthorizedException('Invalid refresh token');
    }

    if (storedToken.isRevoked) {
      // Add a high-priority log for this security event
      this.logger.error(
        `COMPROMISE DETECTED: Revoked token used for user ${storedToken.userId}. Revoking entire token family ${storedToken.familyId}.`,
      );

      // Invalidate the entire family of tokens using the injected service.
      await this.refreshTokenService.revokeTokenFamily(
        storedToken.userId,
        storedToken.familyId,
      );
      throw new UnauthorizedException('Compromised token detected');
    }

    return {
      id: payload.sub,
      email: payload.email,
      name: payload.name,
      tokenId: payload.tokenId,
      familyId: payload.familyId,
    };
  }
}
