import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { randomBytes } from 'crypto';

export interface TokenPayload {
  sub: string;
  email: string;
  name: string;
  iat?: number;
  exp?: number;
}

export interface RefreshTokenPayload {
  sub: string;
  email: string;
  name: string;
  tokenId: string;
  familyId: string;
}

@Injectable()
export class TokenService {
  constructor(private readonly jwtService: JwtService) {}

  generateAccessToken(user: {
    id: string;
    email: string;
    name: string;
  }): string {
    const payload: TokenPayload = {
      sub: user.id,
      email: user.email,
      name: user.name,
    };
    return this.jwtService.sign(payload, {
      secret: process.env.JWT_SECRET,
      expiresIn: '15m',
    });
  }

  generateRefreshToken(
    user: { id: string; email: string; name: string },
    familyId: string,
    tokenId: string,
  ): string {
    const payload: RefreshTokenPayload = {
      sub: user.id,
      email: user.email,
      name: user.name,
      tokenId,
      familyId,
    };
    return this.jwtService.sign(payload, {
      secret: process.env.JWT_REFRESH_SECRET,
      expiresIn: '7d',
    });
  }

  verifyRefreshToken(token: string): RefreshTokenPayload {
    return this.jwtService.verify<RefreshTokenPayload>(token, {
      secret: process.env.JWT_REFRESH_SECRET,
    });
  }

  generateTokenIds() {
    const familyId = randomBytes(32).toString('hex');
    const tokenId = randomBytes(32).toString('hex');
    return { familyId, tokenId };
  }
}
