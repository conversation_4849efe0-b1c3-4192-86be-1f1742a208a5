import { Controller, Get, Post, Req, Res, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Response, Request } from 'express';
import { AuthService } from './auth.service';
import { Throttle, seconds } from '@nestjs/throttler';
import { TokenService } from './token.service';
import { RefreshTokenService } from './refresh-token.service';

@Controller('api/auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly tokenService: TokenService,
    private readonly refreshTokenService: RefreshTokenService,
  ) {}

  @Get('google')
  @UseGuards(AuthGuard('google'))
  async googleAuth() {
    // Initiates the Google OAuth2 login flow
  }

  @Get('google/callback')
  @UseGuards(AuthGuard('google'))
  async googleAuthRedirect(@Req() req, @Res() res: Response) {
    const { accessToken, refreshToken } = await this.authService.login(
      req.user,
    );

    res.cookie('access_token', accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 15 * 60 * 1000, // 15 minutes
    });

    res.cookie('refresh_token', refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    });

    res.redirect(process.env.FRONTEND_CALLBACK_URL);
  }

  @Get('profile')
  @UseGuards(AuthGuard('jwt'))
  getProfile(@Req() req) {
    return req.user;
  }

  @Post('refresh')
  @Throttle({ default: { limit: 50, ttl: seconds(60) } })
  async refresh(@Req() req: Request, @Res() res: Response) {
    const refreshToken = req.cookies?.refresh_token;

    if (!refreshToken) {
      return res.status(401).json({ message: 'Refresh token required' });
    }

    try {
      const tokens = await this.authService.refreshTokens(refreshToken, res);
      return res.json({ accessToken: tokens.accessToken });
    } catch {
      res.clearCookie('access_token');
      res.clearCookie('refresh_token');
      return res.status(401).json({ message: 'Invalid refresh token' });
    }
  }

  @Post('logout')
  @UseGuards(AuthGuard('jwt'))
  @Throttle({ default: { limit: 50, ttl: seconds(60) } })
  async logout(@Req() req: Request, @Res() res: Response) {
    const refreshToken = req.cookies?.refresh_token;
    const user = req.user as { id: string };

    if (refreshToken && user?.id) {
      try {
        const payload = this.tokenService.verifyRefreshToken(refreshToken);
        await this.authService.logout(user.id, payload.tokenId);
      } catch {
        // Ignore invalid token on logout
      }
    }

    res.clearCookie('access_token');
    res.clearCookie('refresh_token');
    res.status(200).json({ message: 'Logged out successfully' });
  }

  @Post('revoke-all')
  @UseGuards(AuthGuard('jwt'))
  @Throttle({ default: { limit: 20, ttl: seconds(60) } })
  async revokeAll(@Req() req: Request, @Res() res: Response) {
    const user = req.user as { id: string };
    await this.refreshTokenService.revokeAllUserTokens(user.id);

    res.clearCookie('access_token');
    res.clearCookie('refresh_token');
    res.status(200).json({ message: 'All sessions revoked' });
  }
}
