import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { RefreshToken } from '@prisma/client';

@Injectable()
export class RefreshTokenService {
  constructor(private readonly prisma: PrismaService) {}

  async createRefreshToken(
    userId: string,
    familyId: string,
    tokenId: string,
  ): Promise<RefreshToken> {
    return this.prisma.refreshToken.create({
      data: {
        userId,
        familyId,
        tokenId,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
      },
    });
  }

  async findRefreshToken(tokenId: string): Promise<RefreshToken | null> {
    return this.prisma.refreshToken.findUnique({
      where: { tokenId },
    });
  }

  async validateRefreshToken(tokenId: string): Promise<RefreshToken> {
    const storedToken = await this.findRefreshToken(tokenId);

    if (
      !storedToken ||
      storedToken.isRevoked ||
      storedToken.expiresAt < new Date()
    ) {
      throw new UnauthorizedException('Invalid or expired refresh token');
    }

    return storedToken;
  }

  async revokeRefreshToken(tokenId: string): Promise<RefreshToken> {
    return this.prisma.refreshToken.update({
      where: { tokenId },
      data: { isRevoked: true },
    });
  }

  async revokeTokenFamily(userId: string, familyId: string) {
    await this.prisma.refreshToken.updateMany({
      where: { userId, familyId },
      data: { isRevoked: true },
    });
  }

  async revokeAllUserTokens(userId: string) {
    await this.prisma.refreshToken.updateMany({
      where: { userId },
      data: { isRevoked: true },
    });
  }
}
