import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { Response } from 'express';
import { TokenService } from './token.service';
import { RefreshTokenService } from './refresh-token.service';

@Injectable()
export class AuthService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly tokenService: TokenService,
    private readonly refreshTokenService: RefreshTokenService,
  ) {}

  async validateUser(details: {
    email: string;
    displayName: string;
    googleId: string;
    picture: string;
  }) {
    const user = await this.prisma.user.findUnique({
      where: { email: details.email },
    });

    if (user) {
      return this.prisma.user.update({
        where: { email: details.email },
        data: {
          googleId: user.googleId ?? details.googleId,
          picture: user.picture ?? details.picture,
        },
      });
    }

    return this.prisma.user.create({
      data: {
        email: details.email,
        name: details.displayName,
        googleId: details.googleId,
        picture: details.picture,
      },
    });
  }

  async login(user: { id: string; email: string; name: string }) {
    const { familyId, tokenId } = this.tokenService.generateTokenIds();

    const accessToken = this.tokenService.generateAccessToken(user);
    const refreshToken = this.tokenService.generateRefreshToken(
      user,
      familyId,
      tokenId,
    );

    await this.refreshTokenService.createRefreshToken(
      user.id,
      familyId,
      tokenId,
    );

    return { accessToken, refreshToken };
  }

  async refreshTokens(refreshToken: string, res: Response) {
    try {
      const payload = this.tokenService.verifyRefreshToken(refreshToken);
      const storedToken = await this.refreshTokenService.validateRefreshToken(
        payload.tokenId,
      );

      await this.refreshTokenService.revokeRefreshToken(storedToken.tokenId);

      const { tokenId: newRefreshTokenId } =
        this.tokenService.generateTokenIds();

      const newAccessToken = this.tokenService.generateAccessToken({
        id: payload.sub,
        email: payload.email,
        name: payload.name,
      });
      const newRefreshToken = this.tokenService.generateRefreshToken(
        { id: payload.sub, email: payload.email, name: payload.name },
        storedToken.familyId,
        newRefreshTokenId,
      );

      await this.refreshTokenService.createRefreshToken(
        payload.sub,
        storedToken.familyId,
        newRefreshTokenId,
      );

      this.setAuthCookies(res, newAccessToken, newRefreshToken);

      return { accessToken: newAccessToken, refreshToken: newRefreshToken };
    } catch {
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  async logout(userId: string, refreshTokenId: string) {
    const token =
      await this.refreshTokenService.findRefreshToken(refreshTokenId);
    if (token) {
      await this.refreshTokenService.revokeTokenFamily(userId, token.familyId);
    }
  }

  private setAuthCookies(
    res: Response,
    accessToken: string,
    refreshToken: string,
  ) {
    res.cookie('access_token', accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 15 * 60 * 1000, // 15 minutes
    });

    res.cookie('refresh_token', refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    });
  }
}
