import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ChatController } from './chat.controller';
import { ChatService } from './services/chat.service';
import { OpenRouterService } from './services/openrouter.service';
import { GeminiService } from './services/gemini.service';
import { PrismaService } from '../prisma/prisma.service';

@Module({
  imports: [ConfigModule],
  controllers: [ChatController],
  providers: [ChatService, OpenRouterService, GeminiService, PrismaService],
})
export class ChatModule {}
