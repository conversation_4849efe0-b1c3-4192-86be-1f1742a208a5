import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import OpenAI from 'openai';
import { backOff } from 'exponential-backoff';

interface ChatCompletionParams {
  messages: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string;
  }>;
  model?: string;
  stream?: boolean;
}

@Injectable()
export class OpenRouterService {
  private readonly logger = new Logger(OpenRouterService.name);
  private openai: OpenAI;

  constructor(private configService: ConfigService) {
    this.openai = new OpenAI({
      baseURL: 'https://openrouter.ai/api/v1',
      apiKey: this.configService.get<string>('OPENROUTER_API_KEY'),
      defaultHeaders: {
        'HTTP-Referer': this.configService.get<string>(
          'FRONTEND_URL',
          'http://localhost:3000',
        ),
        'X-Title': 'Revisa.AI',
      },
    });
  }

  async createChatCompletion(params: ChatCompletionParams) {
    const {
      messages,
      model = 'gemini-2.5-flash-lite',
      stream = false,
    } = params;

    try {
      const completion = await backOff(
        () =>
          this.openai.chat.completions.create({
            model,
            messages: [
              {
                role: 'system',
                content:
                  'Você é uma IA especializada em preparação para a FUVEST. Ajude os estudantes com questões, explicações de matérias, estratégias de estudo e orientações para o vestibular da USP.',
              },
              ...messages,
            ],
            stream,
            max_tokens: 16000,
            temperature: 0.7,
          }),
        {
          startingDelay: 10,
          maxDelay: 10000,
          numOfAttempts: 3,
          timeMultiple: 2,
          retry: (e: { status: number; message: string }) => {
            this.logger.warn(`OpenRouter API error: ${e.message}`);
            // Retry on rate limit or server errors
            return e.status === 429 || e.status >= 500;
          },
        },
      );

      return completion;
    } catch (error) {
      this.logger.error(`Failed to create chat completion: ${error.message}`);
      throw error;
    }
  }

  async *streamChatCompletion(params: ChatCompletionParams) {
    const { messages, model = 'gemini-2.5-flash-lite' } = params;

    try {
      const stream = await backOff(
        () =>
          this.openai.chat.completions.create({
            model,
            messages: [
              {
                role: 'system',
                content:
                  'Você é uma IA especializada em preparação para a FUVEST. Ajude os estudantes com questões, explicações de matérias, estratégias de estudo e orientações para o vestibular da USP.',
              },
              ...messages,
            ],
            stream: true,
            max_tokens: 16000,
            temperature: 0.7,
          }),
        {
          startingDelay: 10,
          maxDelay: 10000,
          numOfAttempts: 3,
          timeMultiple: 2,
          retry: (e: { status: number; message: string }) => {
            this.logger.warn(`OpenRouter API error: ${e.message}`);
            return e.status === 429 || e.status >= 500;
          },
        },
      );

      for await (const chunk of stream) {
        const content = chunk.choices[0]?.delta?.content || '';
        if (content) {
          yield content;
        }
      }
    } catch (error) {
      this.logger.error(`Failed to stream chat completion: ${error.message}`);
      throw error;
    }
  }
}
