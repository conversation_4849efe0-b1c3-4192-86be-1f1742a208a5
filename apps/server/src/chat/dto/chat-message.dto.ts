import { IsString, IsNotEmpty, IsOptional } from 'class-validator';

export class CreateChatMessageDto {
  @IsString()
  @IsNotEmpty()
  content: string;

  @IsString()
  @IsNotEmpty()
  model: string;

  @IsString()
  @IsOptional()
  threadId?: string;

  @IsString()
  @IsOptional()
  parentId?: string; // ID of the previous message

  @IsString()
  @IsOptional()
  idempotencyKey?: string;
}

export class EditChatMessageDto {
  @IsString()
  @IsNotEmpty()
  content: string;
}

export class NavigateSiblingDto {
  @IsString()
  @IsNotEmpty()
  messageId: string;

  @IsString()
  @IsNotEmpty()
  direction: 'next' | 'prev';
}

export class ChatMessageResponseDto {
  id: string;
  content: string;
  role: 'USER' | 'ASSISTANT' | 'SYSTEM';
  createdAt: string;
  model?: string;
  threadId?: string;
  parentId?: string | null;

  // Tree navigation properties
  siblingCount: number;
  siblingPosition: number;
  deletedAt?: string | null;
}

export class CreateThreadDto {
  @IsString()
  @IsNotEmpty()
  title: string;
}
