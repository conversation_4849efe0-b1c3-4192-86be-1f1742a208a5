# Server-specific variables
NODE_ENV=development
PORT=3001

# Google OAuth 2.0
GOOGLE_CALLBACK_URL=http://localhost:3001/api/auth/google/callback
FRONTEND_CALLBACK_URL=http://localhost:3000/auth/callback

# Database and Redis URLs for local development
# These are used when running `npm run dev` directly
DATABASE_URL=postgresql://revisa_user:revisa_password@localhost:5432/revisa_dev
REDIS_URL=redis://:revisa_redis_password@localhost:6379

# Secrets and other configs
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_REFRESH_SECRET=your-super-secret-jwt-refresh-key-change-this-in-production
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
OPENAI_API_KEY=sk-your-openai-api-key