#!/bin/sh

set -euo pipefail

MAX_RETRIES=5
RETRY_DELAY=5

echo "→ Running Prisma migrations with retry (max $MAX_RETRIES attempts)..."

attempt=1
until npx prisma migrate deploy; do
  if [ $attempt -ge $MAX_RETRIES ]; then
    echo "✖ Migration failed after $attempt attempts. Exiting."
    exit 1
  fi
  echo "⚠ Migration attempt $attempt failed—retrying in ${RETRY_DELAY}s..."
  attempt=$((attempt + 1))
  sleep $RETRY_DELAY
done

echo "✔ Migrations applied successfully."

echo "→ Starting application"
exec node dist/main.js
