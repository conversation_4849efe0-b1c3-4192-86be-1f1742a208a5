{"name": "server", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "dotenv -e .env -- nest start", "start:dev": "dotenv -e .env -- nest start --watch", "start:debug": "dotenv -e .env -- nest start --debug --watch", "start:prod": "dotenv -e .env -- node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.0", "@nestjs/platform-express": "^11.0.1", "@nestjs/terminus": "^11.0.0", "@nestjs/throttler": "^6.4.0", "@prisma/client": "^6.11.1", "bcrypt": "^6.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "cookie-parser": "^1.4.6", "exponential-backoff": "^3.1.2", "helmet": "^8.1.0", "openai": "^5.9.2", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "redis": "^5.6.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "uuid": "^11.1.0"}, "devDependencies": {"@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@revisa/eslint-config": "*", "@revisa/typescript-config": "*", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.6", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/passport-google-oauth20": "^2.0.16", "@types/passport-jwt": "^4.0.1", "@types/redis": "^4.0.10", "@types/supertest": "^6.0.2", "@types/uuid": "^10.0.0", "dotenv-cli": "^8.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "prisma": "^6.11.1", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}