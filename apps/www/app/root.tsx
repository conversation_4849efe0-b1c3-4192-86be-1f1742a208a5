import '@fontsource/geist-sans';
import '@fontsource/geist-mono';
import {
  isRouteErrorResponse,
  Links,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration,
} from 'react-router';
import { useEffect } from 'react';
import { toast } from 'sonner';

import type { Route } from './+types/root';
import './app.css';
import { AuthProvider } from './auth/auth-provider';
import { Toaster } from '~/components/sonner';

export const links: Route.LinksFunction = () => [];

export function Layout({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    const handleApiError = (
      event: CustomEvent<{
        message: string;
        description: string;
        action?: { label: string; onClick: () => void };
      }>,
    ) => {
      const { message, description, action } = event.detail;
      toast.error(message, { description, action });
    };

    window.addEventListener('apiError', handleApiError as EventListener);

    return () => {
      window.removeEventListener('apiError', handleApiError as EventListener);
    };
  }, []);

  return (
    <html lang="pt-BR">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Meta />
        <Links />
      </head>
      <body>
        <AuthProvider>
          {children}
          <Toaster />
          <ScrollRestoration />
          <Scripts />
        </AuthProvider>
      </body>
    </html>
  );
}

export default function App() {
  return <Outlet />;
}

export function ErrorBoundary({ error }: Route.ErrorBoundaryProps) {
  let message = 'Oops!';
  let details = 'An unexpected error occurred.';
  let stack: string | undefined;

  if (isRouteErrorResponse(error)) {
    message = error.status === 404 ? '404' : 'Error';
    details =
      error.status === 404
        ? 'The requested page could not be found.'
        : error.statusText || details;
  } else if (import.meta.env.DEV && error && error instanceof Error) {
    details = error.message;
    stack = error.stack;
  }

  return (
    <main className="pt-16 p-4 container mx-auto">
      <h1>{message}</h1>
      <p>{details}</p>
      {stack && (
        <pre className="w-full p-4 overflow-x-auto bg-muted text-muted-foreground rounded-md mt-4">
          <code>{stack}</code>
        </pre>
      )}
    </main>
  );
}
