import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import type { ChatMessage, Thread } from '~/types/chat';

export interface ChatState {
  threads: Thread[];
  currentThreadId: string | null;
  messagesByThread: Record<string, ChatMessage[]>;
  streamingResponses: Record<string, ChatMessage>;
  threadsStatus: 'idle' | 'loading' | 'success' | 'error';
  inputValue: string;
  unreadThreads: string[];
}

interface ChatActions {
  setThreads: (threads: Thread[]) => void;
  addThread: (thread: Thread) => void;
  updateThread: (threadId: string, title: string) => void;
  setThreadsStatus: (status: ChatState['threadsStatus']) => void;
  setCurrentThreadId: (threadId: string | null) => void;
  setMessagesForThread: (threadId: string, messages: ChatMessage[]) => void;
  addMessageToThread: (threadId: string, message: ChatMessage) => void;
  replaceMessageInThread: (
    threadId: string,
    messageIdToReplace: string,
    newMessage: ChatMessage,
  ) => void;
  removeMessageFromThread: (threadId: string, messageId: string) => void;
  startStreaming: (threadId: string, message: ChatMessage) => void;
  updateStreamingContent: (
    threadId: string,
    contentChunk: string,
    debugInfo?: ChatMessage['debug'],
  ) => void;
  finalizeStreaming: (threadId: string, finalMessage: ChatMessage) => void;
  abortStreaming: (threadId: string) => void;
  getThreadById: (id: string | null) => Thread | undefined;
  setInputValue: (value: string) => void;
  addUnreadThread: (threadId: string) => void;
  removeUnreadThread: (threadId: string) => void;
}

type ChatStore = ChatState & ChatActions;

const THIRTY_MINUTES_IN_MS = 30 * 60 * 1000;

export const useChatStore = create<ChatStore>()(
  persist(
    (set, get) => ({
      // --- INITIAL STATE ---
      threads: [],
      currentThreadId: null,
      messagesByThread: {},
      streamingResponses: {},
      threadsStatus: 'idle',
      inputValue: '',
      unreadThreads: [],

      // --- ACTIONS ---
      setThreads: (threads) => set({ threads }),
      addThread: (thread) =>
        set((state) => ({ threads: [thread, ...state.threads] })),
      updateThread: (threadId, title) =>
        set((state) => ({
          threads: state.threads.map((t) =>
            t.id === threadId ? { ...t, title } : t,
          ),
        })),
      setThreadsStatus: (status) => set({ threadsStatus: status }),
      setCurrentThreadId: (threadId) => set({ currentThreadId: threadId }),
      setMessagesForThread: (threadId, messages) =>
        set((state) => ({
          messagesByThread: {
            ...state.messagesByThread,
            [threadId]: messages,
          },
        })),
      addMessageToThread: (threadId, message) =>
        set((state) => ({
          messagesByThread: {
            ...state.messagesByThread,
            [threadId]: [...(state.messagesByThread[threadId] || []), message],
          },
        })),
      replaceMessageInThread: (threadId, messageIdToReplace, newMessage) =>
        set((state) => ({
          messagesByThread: {
            ...state.messagesByThread,
            [threadId]: (state.messagesByThread[threadId] || []).map((m) =>
              m.id === messageIdToReplace ? newMessage : m,
            ),
          },
        })),
      removeMessageFromThread: (threadId, messageId) =>
        set((state) => ({
          messagesByThread: {
            ...state.messagesByThread,
            [threadId]: (state.messagesByThread[threadId] || []).filter(
              (m) => m.id !== messageId,
            ),
          },
        })),
      startStreaming: (threadId, message) => {
        set((state) => ({
          streamingResponses: {
            ...state.streamingResponses,
            [threadId]: message,
          },
        }));
      },
      updateStreamingContent: (threadId, contentChunk, debugInfo) => {
        set((state) => {
          const streamingMessage = state.streamingResponses[threadId];
          if (!streamingMessage) return {};
          return {
            streamingResponses: {
              ...state.streamingResponses,
              [threadId]: {
                ...streamingMessage,
                content: streamingMessage.content + contentChunk,
                ...(debugInfo && { debug: debugInfo }),
              },
            },
          };
        });
      },
      finalizeStreaming: (threadId, finalMessage) => {
        get().addMessageToThread(threadId, finalMessage);
        set((state) => {
          const { [threadId]: _, ...rest } = state.streamingResponses;
          return { streamingResponses: rest };
        });
      },
      abortStreaming: (threadId) => {
        set((state) => {
          const { [threadId]: _, ...rest } = state.streamingResponses;
          return { streamingResponses: rest };
        });
      },
      getThreadById: (id) => get().threads.find((t) => t.id === id),
      setInputValue: (value) => set({ inputValue: value }),
      addUnreadThread: (threadId) =>
        set((state) => ({
          // Use a Set to prevent duplicates
          unreadThreads: [...new Set([...state.unreadThreads, threadId])],
        })),
      removeUnreadThread: (threadId) =>
        set((state) => ({
          unreadThreads: state.unreadThreads.filter((id) => id !== threadId),
        })),
    }),
    {
      name: 'chat-storage', // storage name
      storage: createJSONStorage(() => sessionStorage), // use sessionStorage
      // On save, create an object with the value and a timestamp
      partialize: (state) => ({
        inputValue: {
          value: state.inputValue,
          timestamp: Date.now(),
        },
      }),
      // On load, check the timestamp before merging the state
      merge: (persistedState, currentState) => {
        const typedState = persistedState as {
          inputValue?: { value: string; timestamp: number };
        };

        if (typedState.inputValue) {
          const { timestamp } = typedState.inputValue;
          if (Date.now() - timestamp > THIRTY_MINUTES_IN_MS) {
            // Data is stale, ignore it and return the initial state.
            return currentState;
          }
        }

        // Data is fresh, so we manually merge the unwrapped value.
        return {
          ...currentState,
          ...(persistedState as object),
          inputValue: typedState.inputValue?.value || currentState.inputValue,
        };
      },
    },
  ),
);
