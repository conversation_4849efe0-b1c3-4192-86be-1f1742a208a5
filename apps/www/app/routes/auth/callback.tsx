import { useEffect } from 'react';
import { useNavigate } from 'react-router';
import { useAuth } from '~/auth/useAuth';
import { Skeleton } from '~/components/ui/skeleton';

export default function AuthCallback() {
  const { fetchUser } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    const handleAuth = async () => {
      await fetchUser();
      navigate('/');
    };
    handleAuth();
  }, [fetchUser, navigate]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-background">
      <div className="w-full max-w-md p-8 space-y-4 bg-card rounded-lg shadow">
        <h2 className="text-2xl font-bold text-center">Autenticando...</h2>
        <p className="text-center text-muted-foreground">
          Por favor, aguarde enquanto finalizamos seu login.
        </p>
        <div className="space-y-4">
          <Skeleton className="h-8 w-full" />
          <Skeleton className="h-8 w-full" />
        </div>
      </div>
    </div>
  );
}
