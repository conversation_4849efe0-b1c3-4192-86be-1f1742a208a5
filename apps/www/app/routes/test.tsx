// apps/www/app/routes/test.tsx
import { useState } from 'react';
import { toast } from 'sonner';
import { useAuth } from '~/auth/useAuth';
import { useApi } from '~/hooks/useApi';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import { Button } from '~/components/ui/button';

export default function TestPage() {
  const [testData, setTestData] = useState<Record<string, unknown> | null>(
    null,
  );
  const [userData, setUserData] = useState<Record<string, unknown> | null>(
    null,
  );
  const [typingText, setTypingText] = useState('');
  const [progress, setProgress] = useState(0);
  const [isTypingComplete, setIsTypingComplete] = useState(false);
  const [isProgressComplete, setIsProgressComplete] = useState(false);

  const { isAuthenticated, login } = useAuth();
  const { get, loading } = useApi();

  const fetchTestData = async () => {
    const data = await get<Record<string, unknown>>('/api/test');
    if (data) setTestData(data);
  };

  const fetchUserData = async () => {
    const userId = 'a1b2c3d4-e5f6-7890-1234-567890abcdef'; // Example UUID
    const data = await get<Record<string, unknown>>(`/api/user/${userId}`);
    if (data) setUserData(data);
  };

  const startSseStream = (
    url: string,
    onMessage: (data: Record<string, unknown>) => void,
    onComplete: () => void,
  ) => {
    if (!isAuthenticated) {
      toast.error('Login Required', {
        description: 'Please log in to start the stream.',
        action: {
          label: 'Login',
          onClick: () => login(),
        },
      });
      return;
    }

    const ctrl = new AbortController();

    fetchEventSource(url, {
      signal: ctrl.signal,
      credentials: 'include', // Ensures cookies are sent with the request

      async onopen(response) {
        if (response.ok) {
          return; // Connection is successful
        }

        if (response.status === 429) {
          throw new Error(
            'You have made too many requests. Please wait a moment before trying again.',
          );
        }

        throw new Error(
          `Failed to connect with status ${response.status}: ${response.statusText}`,
        );
      },

      onmessage(event) {
        // Only parse if the event data is not empty
        if (event.data) {
          const parsedData = JSON.parse(event.data);
          onMessage(parsedData);
          if (parsedData.isComplete) {
            onComplete();
            ctrl.abort(); // Close the connection on completion
          }
        }
      },

      onerror(err) {
        // This will now catch errors from onopen or other network issues
        toast.error('Stream Connection Error', {
          description:
            err.message || 'An unknown error occurred during the stream.',
        });
        ctrl.abort(); // Ensure connection is closed
        // Re-throwing the error is important to stop the library from retrying
        throw err;
      },
    });
  };

  const startTypingStream = () => {
    setTypingText('');
    setIsTypingComplete(false);
    startSseStream(
      '/api/stream/typing',
      (data) => setTypingText((prev) => prev + (data.char as string)),
      () => setIsTypingComplete(true),
    );
  };

  const startProgressStream = () => {
    setProgress(0);
    setIsProgressComplete(false);
    startSseStream(
      '/api/stream/progress',
      (data) => setProgress(data.progress as number),
      () => setIsProgressComplete(true),
    );
  };

  return (
    <div className="min-h-screen bg-background py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-foreground mb-8 text-center">
          Railway Connection Test
        </h1>

        <div className="grid gap-6">
          {/* HTTP Requests Section */}
          <div className="bg-card text-card-foreground rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">
              HTTP Requests (with Global Error Handling)
            </h2>
            <div className="space-y-4">
              <div>
                <Button onClick={fetchTestData} disabled={loading}>
                  {loading ? 'Loading...' : 'Fetch Test Data'}
                </Button>
                {testData && (
                  <div className="mt-3 p-3 bg-muted rounded-md">
                    <pre className="text-sm text-muted-foreground">
                      {JSON.stringify(testData, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
              <div>
                <Button
                  onClick={fetchUserData}
                  disabled={loading}
                  variant="secondary"
                >
                  {loading ? 'Loading...' : 'Fetch User Data (Needs Auth)'}
                </Button>
                {userData && (
                  <div className="mt-3 p-3 bg-muted rounded-md">
                    <pre className="text-sm text-muted-foreground">
                      {JSON.stringify(userData, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* SSE Streaming Section */}
          <div className="bg-card text-card-foreground rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">
              Server-Sent Events (SSE) Streaming (Needs Auth)
            </h2>
            <div className="space-y-6">
              {/* Typing Animation */}
              <div>
                <Button
                  onClick={startTypingStream}
                  className="bg-purple-500 hover:bg-purple-600"
                >
                  Start Typing Animation
                </Button>
                <div className="mt-3 p-4 bg-muted rounded-md min-h-[100px]">
                  <p className="text-muted-foreground leading-relaxed">
                    {typingText}
                    {!isTypingComplete && typingText && (
                      <span className="animate-pulse">|</span>
                    )}
                  </p>
                  {isTypingComplete && (
                    <p className="text-green-600 dark:text-green-400 text-sm mt-2">
                      ✓ Typing complete!
                    </p>
                  )}
                </div>
              </div>

              {/* Progress Bar */}
              <div>
                <Button
                  onClick={startProgressStream}
                  className="bg-orange-500 hover:bg-orange-600"
                >
                  Start Progress Stream
                </Button>
                <div className="mt-3">
                  <div className="flex justify-between text-sm text-muted-foreground mb-1">
                    <span>Progress</span>
                    <span>{progress}%</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div
                      className="bg-primary h-2 rounded-full transition-all duration-200"
                      style={{ width: `${progress}%` }}
                    ></div>
                  </div>
                  {isProgressComplete && (
                    <p className="text-green-600 dark:text-green-400 text-sm mt-2">
                      ✓ Progress complete!
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
