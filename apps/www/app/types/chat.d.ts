// apps/www/app/types/chat.ts

/**
 * Represents a single message in a conversation, including UI-specific state.
 */
export interface ChatMessage {
  id: string;
  content: string;
  role: 'USER' | 'ASSISTANT' | 'SYSTEM';
  createdAt: string;
  model?: string;
  threadId: string;
  parentId: string | null;
  siblingCount: number;
  siblingPosition: number;
  deletedAt?: string | null;
  debug?: {
    serverTimestamp?: number;
    tps?: number;
    ping?: number;
    avgPing?: number;
    maxPing?: number;
    avgTps?: number;
    maxTps?: number;
  };
  /** Optional title for the thread, returned with the first message of a new thread. */
  threadTitle?: string;
}

/**
 * Represents a conversation thread.
 */
export interface Thread {
  id: string;
  title: string;
  createdAt: string;
  updatedAt: string;
  // This might not be needed on the summary object, but is fine for now
  messages: ChatMessage[];
}
