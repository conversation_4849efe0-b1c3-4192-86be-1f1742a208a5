import { Skeleton } from '~/components/ui/skeleton';

export function MainChatSkeleton() {
  return (
    <div className="flex-1 flex flex-col">
      {/* Header */}
      <div className="h-14 border-b border-border px-6 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Skeleton className="h-8 w-8 rounded-full" />
          <Skeleton className="h-4 w-20" />
        </div>
        <div className="flex items-center gap-2">
          <Skeleton className="h-8 w-8" />
          <Skeleton className="h-8 w-8" />
        </div>
      </div>
      {/* Message List */}
      <div className="flex-1 p-6 space-y-8">
        {/* AI message skeleton */}
        <div className="flex gap-3">
          <Skeleton className="h-8 w-8 rounded-full shrink-0" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-4 w-4/5" />
            <Skeleton className="h-4 w-3/5" />
          </div>
        </div>
        {/* User message skeleton */}
        <div className="flex gap-3 items-end flex-row-reverse">
          <Skeleton className="h-8 w-8 rounded-full shrink-0" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-24 self-end" />
            <Skeleton className="h-4 w-2/5 self-end" />
          </div>
        </div>
        {/* AI message skeleton */}
        <div className="flex gap-3">
          <Skeleton className="h-8 w-8 rounded-full shrink-0" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-4/6" />
          </div>
        </div>
      </div>
      {/* Input */}
      <div className="min-h-16 border-t border-border">
        <div className="mx-auto max-w-4xl p-3 flex items-end gap-3">
          <div className="h-8 w-8 shrink-0" />
          <div className="flex-1 flex gap-2 items-end w-full">
            <Skeleton className="h-9 w-full rounded-md" />
            <Skeleton className="h-9 w-9 rounded-lg" />
          </div>
        </div>
      </div>
    </div>
  );
}
