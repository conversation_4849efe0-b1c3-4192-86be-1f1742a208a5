import { useRef, useEffect, useLayoutEffect } from 'react';
import { useShallow } from 'zustand/react/shallow';
import { useParams } from 'react-router';

import { ChatSidebar } from '~/components/chat/chat-sidebar';
import { ChatHeader } from '~/components/chat/chat-header';
import { MessageList } from '~/components/chat/message-list';
import { ChatInput } from '~/components/chat/chat-input';
import { useChatActions } from '~/hooks/useChatActions';
import { useChatForm } from '~/hooks/useChatForm';
import { useMediaQuery } from '~/hooks/useMediaQuery';
import { useChatStore } from '~/store/chatStore';
import { MainChatSkeleton } from '~/components/chat-skeleton';
import * as chatService from '~/services/chat-service';
import { useAuth } from '~/auth/useAuth';

const EMPTY_MESSAGES: [] = [];

export function ChatInterface() {
  const { sendMessage, createNewThread } = useChatActions();
  const { threadId: urlThreadId } = useParams<{ threadId?: string }>();
  const { isAuthenticated } = useAuth();

  // Actions and static data can be pulled non-reactively or with stable selectors
  const {
    setMessagesForThread,
    setCurrentThreadId,
    setThreads,
    setThreadsStatus,
    removeUnreadThread,
  } = useChatStore.getState();
  const { threads, threadsStatus, unreadThreads } = useChatStore(
    useShallow((state) => ({
      threads: state.threads,
      threadsStatus: state.threadsStatus,
      unreadThreads: state.unreadThreads,
    })),
  );

  // --- Effect to fetch threads list ---
  useEffect(() => {
    // Only fetch if authenticated and threads haven't been loaded/are not loading
    if (isAuthenticated && threadsStatus === 'idle') {
      setThreadsStatus('loading');
      chatService
        .fetchThreads()
        .then((fetchedThreads) => {
          setThreads(fetchedThreads);
          setThreadsStatus('success');
        })
        .catch((err) => {
          console.error('Failed to fetch threads:', err);
          setThreadsStatus('error');
        });
    }
  }, [isAuthenticated, threadsStatus, setThreads, setThreadsStatus]);

  // --- Smart Data Fetching & State Sync ---
  // This effect synchronizes the URL, fetches data, and marks threads as read.
  useEffect(() => {
    const currentId = urlThreadId || null;
    setCurrentThreadId(currentId);

    // If we've navigated to a thread, mark it as read.
    if (currentId) {
      removeUnreadThread(currentId);
    }

    // Fetch messages only if a thread is selected and its messages are truly missing.
    if (
      currentId &&
      useChatStore.getState().messagesByThread[currentId] === undefined
    ) {
      // Set to an empty array immediately to mark as 'loading' and prevent re-fetches.
      setMessagesForThread(currentId, []);
      chatService
        .fetchMessages(currentId)
        .then((messages) => setMessagesForThread(currentId, messages))
        .catch((err) => {
          console.error(
            `Failed to fetch messages for thread ${currentId}:`,
            err,
          );
          // TODO: Handle UI error state, maybe remove the thread entry or show toast.
        });
    }
  }, [
    urlThreadId,
    setCurrentThreadId,
    setMessagesForThread,
    removeUnreadThread,
  ]);

  // --- Optimized Data Subscription ---
  // This selector is now highly optimized. It only subscribes the component to
  // changes for the currently active thread, preventing re-renders from prefetching.
  const { messages, streamingResponse, isAiTyping, currentThreadId } =
    useChatStore(
      useShallow((state) => {
        const id = state.currentThreadId;
        const msgs = id ? state.messagesByThread[id] : EMPTY_MESSAGES;
        const stream = id ? state.streamingResponses[id] : null;
        return {
          messages: msgs === undefined ? EMPTY_MESSAGES : msgs, // Handle loading case
          streamingResponse: stream,
          isAiTyping: !!stream,
          currentThreadId: id,
        };
      }),
    );

  const {
    inputValue,
    handleInputChange,
    handleEnterKeyPress,
    handleSendMessageClick,
    textareaRef,
  } = useChatForm({
    onSubmit: sendMessage,
    isAiTyping,
  });

  const isSmallScreen = useMediaQuery('(max-width: 1023px)');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const isInitialLoad = useRef(true);

  useEffect(() => {
    isInitialLoad.current = true;
  }, [currentThreadId]);

  useLayoutEffect(() => {
    const shouldScroll = messages.length > 0 || streamingResponse;
    if (!shouldScroll) return;

    const isStreaming = !!streamingResponse?.content;
    const behavior = isInitialLoad.current || isStreaming ? 'auto' : 'smooth';

    messagesEndRef.current?.scrollIntoView({ behavior, block: 'end' });

    if (isInitialLoad.current) {
      isInitialLoad.current = false;
    }
  }, [messages, streamingResponse?.content]);

  // A thread is loading if the URL says we're on it, but its message array
  // in the store is `undefined` (meaning we haven't started fetching for it yet).
  const isLoading = urlThreadId
    ? useChatStore.getState().messagesByThread[urlThreadId] === undefined
    : false;

  return (
    <div className="flex h-screen bg-background">
      <ChatSidebar
        threads={threads}
        currentThreadId={currentThreadId}
        onNewThread={createNewThread}
        unsentMessage={inputValue}
        unreadThreads={unreadThreads}
      />
      {isLoading ? (
        <MainChatSkeleton />
      ) : (
        <div className="flex-1 flex flex-col">
          <ChatHeader />
          <MessageList
            messages={messages}
            streamingResponse={streamingResponse}
            messagesEndRef={messagesEndRef}
          />
          <ChatInput
            inputValue={inputValue}
            handleInputChange={handleInputChange}
            handleEnterKeyPress={handleEnterKeyPress}
            handleSendMessage={handleSendMessageClick}
            textareaRef={textareaRef}
            isSmallScreen={isSmallScreen}
            isAiTyping={isAiTyping}
          />
        </div>
      )}
    </div>
  );
}
