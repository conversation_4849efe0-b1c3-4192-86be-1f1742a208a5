import * as React from 'react';
import { Plus, Setting<PERSON>, ArrowLeft } from 'lucide-react';
import { Button } from '~/components/ui/button';

export function ChatHeader() {
  return (
    <header className="h-14 border-b border-border px-6 flex items-center justify-between">
      <div className="flex items-center gap-3">
        <Button variant="ghost" size="icon" className="h-8 w-8">
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="font-semibold text-sm">Chat IA</h1>
      </div>
      <div className="flex items-center gap-2">
        <Button variant="ghost" size="icon" className="h-8 w-8">
          <Plus className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="icon" className="h-8 w-8">
          <Settings className="h-4 w-4" />
        </Button>
      </div>
    </header>
  );
}
