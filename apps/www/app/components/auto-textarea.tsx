import * as React from 'react';
import { clsx } from 'clsx';

export interface AutoTextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  onEnterKeyPress?: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void;
}

export type AutoTextareaRef = {
  reset: () => void;
  focus: () => void;
};

const AutoTextarea = React.forwardRef<AutoTextareaRef, AutoTextareaProps>(
  (
    { className, onEnterKeyPress, onChange, onKeyDown, value, ...props },
    ref,
  ) => {
    const textareaRef = React.useRef<HTMLTextAreaElement>(null);

    // Merge refs
    React.useImperativeHandle(ref, () => ({
      reset: () => {
        if (textareaRef.current) {
          textareaRef.current.style.height = '36px';
        }
      },
      focus: () => {
        textareaRef.current?.focus();
      },
    }));

    const adjustHeight = React.useCallback(() => {
      const textarea = textareaRef.current;
      if (textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = `${Math.min(textarea.scrollHeight, 200)}px`; // Max height 200px
      }
    }, []);

    const handleKeyDown = React.useCallback(
      (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
        let shouldSendMessage = false;

        if (e.key === 'Enter') {
          const hasLineBreaks = (
            e.target as HTMLTextAreaElement
          ).value.includes('\n');

          if (e.shiftKey) {
            // Default behavior: new line. Do nothing special.
          } else if (e.ctrlKey || e.metaKey) {
            // Always send with Ctrl/Cmd+Enter
            shouldSendMessage = true;
          } else {
            // Plain Enter
            if (!hasLineBreaks) {
              // Send on single line
              shouldSendMessage = true;
            }
            // Otherwise, default behavior (new line on multi-line)
          }

          if (shouldSendMessage) {
            e.preventDefault();
            onEnterKeyPress?.(e);
          }
        }

        // Always pass the event to the parent's onKeyDown for other purposes (like tutorials)
        onKeyDown?.(e);
      },
      [onEnterKeyPress, onKeyDown],
    );

    const handleChange = React.useCallback(
      (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        onChange?.(e);
        // Adjust height after change
        adjustHeight();
      },
      [onChange, adjustHeight],
    );

    // Also adjust height if the value is changed programmatically
    React.useEffect(() => {
      adjustHeight();
    }, [value, adjustHeight]);

    return (
      <textarea
        ref={textareaRef}
        className={clsx(
          'flex min-h-9 w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 resize-none overflow-y-hidden',
          className,
        )}
        rows={1}
        value={value}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        {...props}
      />
    );
  },
);
AutoTextarea.displayName = 'AutoTextarea';

export { AutoTextarea };
