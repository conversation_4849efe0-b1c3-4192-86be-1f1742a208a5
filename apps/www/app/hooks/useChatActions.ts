import { useCallback } from 'react';
import { useNavigate } from 'react-router';
import { toast } from 'sonner';
import DOMPurify from 'dompurify';

import { useAuth } from '~/auth/useAuth';
import * as chatService from '~/services/chat-service';
import { useChatStore } from '~/store/chatStore';
import { useChatConnection } from '~/hooks/useChatConnection';

export function useChatActions() {
  const { isAuthenticated, login } = useAuth();
  const navigate = useNavigate();
  const { startStreamingResponse } = useChatConnection(navigate);

  const {
    addThread,
    updateThread,
    setMessagesForThread,
    setCurrentThreadId,
    addMessageToThread,
    replaceMessageInThread,
    removeMessageFromThread,
    abortStreaming,
  } = useChatStore.getState();

  const prefetchThread = useCallback(
    (threadId: string) => {
      const store = useChatStore.getState();
      // Only prefetch if we don't already have the messages for that thread.
      if (!store.messagesByThread[threadId]) {
        console.log(`Prefetching messages for thread: ${threadId}`);
        chatService
          .fetchMessages(threadId)
          .then((msgs) => setMessagesForThread(threadId, msgs))
          .catch((err) =>
            console.error(`Prefetch failed for ${threadId}`, err),
          );
      }
    },
    [setMessagesForThread],
  );

  const createNewThread = useCallback(() => {
    setCurrentThreadId(null);
    navigate('/');
  }, [setCurrentThreadId, navigate]);

  const sendMessage = useCallback(
    async (content: string) => {
      if (!content.trim()) return;
      if (!isAuthenticated) {
        toast.warning('Você precisa estar logado para enviar uma mensagem.', {
          description: 'Sua mensagem foi salva. Faça o login para continuar.',
          action: { label: 'Fazer Login', onClick: () => login(content) },
        });
        return;
      }
      const sanitizedInput = DOMPurify.sanitize(content);
      if (!sanitizedInput.trim()) return;
      const currentThreadId = useChatStore.getState().currentThreadId;
      const processSend = async (threadId: string, parentId: string | null) => {
        const tempUserMessageId = `temp-user-${Date.now()}`;
        addMessageToThread(threadId, {
          id: tempUserMessageId,
          content: sanitizedInput,
          role: 'USER',
          createdAt: new Date().toISOString(),
          threadId,
          parentId,
          siblingCount: 1,
          siblingPosition: 1,
        });
        try {
          const userMessage = await chatService.sendMessage(
            sanitizedInput,
            threadId,
            parentId,
            tempUserMessageId,
          );
          replaceMessageInThread(threadId, tempUserMessageId, userMessage);
          if (userMessage.threadTitle) {
            updateThread(threadId, userMessage.threadTitle);
          }
          startStreamingResponse(userMessage.id, userMessage.threadId);
        } catch {
          toast.error('Error sending message');
          abortStreaming(threadId);
          removeMessageFromThread(threadId, tempUserMessageId);
        }
      };
      if (currentThreadId) {
        const messages =
          useChatStore.getState().messagesByThread[currentThreadId] || [];
        const lastMessageId =
          messages.length > 0 ? messages[messages.length - 1].id : null;
        await processSend(currentThreadId, lastMessageId);
      } else {
        try {
          const newThread = await chatService.createThread(
            sanitizedInput.substring(0, 50),
          );
          addThread(newThread);
          navigate(`/c/${newThread.id}`);
          await processSend(newThread.id, null);
        } catch {
          toast.error('Failed to create new thread.');
        }
      }
    },
    [
      isAuthenticated,
      login,
      startStreamingResponse,
      addMessageToThread,
      replaceMessageInThread,
      removeMessageFromThread,
      abortStreaming,
      updateThread,
      addThread,
      navigate,
    ],
  );

  const saveEdit = useCallback(
    async (messageId: string, newContent: string) => {
      const currentThreadId = useChatStore.getState().currentThreadId;
      if (!newContent.trim() || !currentThreadId) return;

      // Find the message being edited to check its role
      const messages =
        useChatStore.getState().messagesByThread[currentThreadId] || [];
      const messageToEdit = messages.find((m) => m.id === messageId);

      if (!messageToEdit) {
        toast.error('Cannot edit message: original not found.');
        return;
      }

      const sanitizedContent = DOMPurify.sanitize(newContent);
      try {
        const updatedMessages = await chatService.editMessage(
          messageId,
          sanitizedContent,
        );
        setMessagesForThread(currentThreadId, updatedMessages);

        // --- BUG FIX ---
        // Only trigger a new AI response if the edited message was a USER message.
        if (messageToEdit.role === 'USER') {
          toast.success('Branch created. Generating new AI response...');
          const lastMessage = updatedMessages[updatedMessages.length - 1];
          startStreamingResponse(lastMessage.id, currentThreadId);
        } else {
          toast.success('Assistant message updated.');
        }
      } catch (error) {
        toast.error('Failed to update message.');
      }
    },
    [setMessagesForThread, startStreamingResponse],
  );

  const regenerateResponse = useCallback(
    async (aiMessageId: string) => {
      const currentThreadId = useChatStore.getState().currentThreadId;
      if (!currentThreadId) return;
      const messages =
        useChatStore.getState().messagesByThread[currentThreadId] || [];
      const aiMessage = messages.find((m) => m.id === aiMessageId);
      if (!aiMessage || !aiMessage.parentId) {
        toast.error('Cannot regenerate: context not found.');
        return;
      }
      const parentIndex = messages.findIndex(
        (m) => m.id === aiMessage.parentId,
      );
      const newHistory = messages.slice(0, parentIndex + 1);
      setMessagesForThread(currentThreadId, newHistory);
      startStreamingResponse(aiMessage.parentId, currentThreadId, true);
    },
    [setMessagesForThread, startStreamingResponse],
  );

  const deleteMessage = useCallback(
    async (messageId: string) => {
      const currentThreadId = useChatStore.getState().currentThreadId;
      if (!currentThreadId) return;
      try {
        const updatedMessages = await chatService.deleteMessage(messageId);
        setMessagesForThread(currentThreadId, updatedMessages);
        toast.info('Created a new branch with the message deleted.');
      } catch {
        toast.error('Failed to delete message.');
      }
    },
    [setMessagesForThread],
  );

  const navigateSibling = useCallback(
    async (messageId: string, direction: 'next' | 'prev') => {
      const currentThreadId = useChatStore.getState().currentThreadId;
      if (!currentThreadId) return;
      try {
        const messageData = await chatService.navigateSibling(
          messageId,
          direction,
        );
        setMessagesForThread(currentThreadId, messageData);
      } catch (error) {
        toast.error(`No ${direction} version available.`);
      }
    },
    [setMessagesForThread],
  );

  const copyMessage = useCallback((content: string) => {
    navigator.clipboard.writeText(content);
    toast.success('Copied to clipboard!');
  }, []);

  return {
    sendMessage,
    regenerateResponse,
    saveEdit,
    deleteMessage,
    navigateSibling,
    createNewThread,
    copyMessage,
    prefetchThread,
  };
}
