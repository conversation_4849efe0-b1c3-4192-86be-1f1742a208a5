import { useRef, useCallback } from 'react';
import { toast } from 'sonner';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import type { NavigateFunction } from 'react-router';
import { useChatStore } from '~/store/chatStore';
import type { ChatMessage } from '~/types/chat';

// Custom error classes to control retry logic
class FatalError extends Error {}

/**
 * Manages multiple, independent SSE connections for chat responses.
 * Each connection is tied to a specific threadId.
 */
export function useChatConnection(navigate: NavigateFunction) {
  // Use a ref to store a map of active AbortControllers, keyed by threadId.
  const controllersRef = useRef<Record<string, AbortController>>({});

  const {
    startStreaming,
    updateStreamingContent,
    finalizeStreaming,
    abortStreaming,
    getThreadById,
    addUnreadThread,
  } = useChatStore.getState();

  const startStreamingResponse = useCallback(
    async (parentId: string, threadId: string, isRegen = false) => {
      // If a controller for this thread already exists, abort the previous request.
      if (controllersRef.current[threadId]) {
        controllersRef.current[threadId].abort();
      }

      const ctrl = new AbortController();
      controllersRef.current[threadId] = ctrl;

      const streamUrl = isRegen
        ? `/api/chat/stream/${parentId}/regenerate`
        : `/api/chat/stream/${parentId}`;

      // Create a skeleton message and add it to the streaming state immediately.
      // This allows the UI to show a skeleton right away.
      const tempAiMessageId = `temp-ai-${Date.now()}`;
      startStreaming(threadId, {
        id: tempAiMessageId,
        content: '', // Start with empty content
        role: 'ASSISTANT',
        createdAt: new Date().toISOString(),
        threadId: threadId,
        parentId: parentId,
        siblingCount: 1,
        siblingPosition: 1,
      });

      await fetchEventSource(streamUrl, {
        signal: ctrl.signal,
        credentials: 'include',

        onopen: async (response) => {
          if (response.ok) return;
          if (
            response.status >= 400 &&
            response.status < 500 &&
            response.status !== 429
          ) {
            throw new FatalError(`Client Error: ${response.status}`);
          }
        },

        onmessage: (event) => {
          if (ctrl.signal.aborted || !event.data) return;
          const data = JSON.parse(event.data);

          if (data.isComplete && data.finalMessage) {
            const finalMessage = data.finalMessage as ChatMessage;
            finalizeStreaming(threadId, finalMessage);

            // Check if we should show a toast / unread indicator
            const currentThreadId = useChatStore.getState().currentThreadId;
            if (threadId !== currentThreadId) {
              addUnreadThread(threadId); // Mark thread as unread
              const thread = getThreadById(threadId);
              toast.success(`New message in "${thread?.title || 'a chat'}"`, {
                description: finalMessage.content.substring(0, 100) + '...',
                action: {
                  label: 'View',
                  onClick: () => navigate(`/c/${threadId}`),
                },
              });
            }
            ctrl.abort(); // End this connection
            return;
          }

          const { content, debug } = data;
          updateStreamingContent(threadId, content, debug);
        },

        onclose: () => {
          // If the connection closes unexpectedly, clean up the streaming state for that thread.
          abortStreaming(threadId);
          delete controllersRef.current[threadId];
        },

        onerror: (err) => {
          abortStreaming(threadId); // Clean up on error
          delete controllersRef.current[threadId];

          if (err instanceof FatalError) {
            toast.error('A fatal error occurred.', {
              description: 'Please check your connection or try again later.',
            });
            throw err; // Stop retrying
          }
          toast.warning('Connection issue. Retrying...');
          return 2000; // Retry delay
        },
      });
    },
    [
      startStreaming,
      updateStreamingContent,
      finalizeStreaming,
      abortStreaming,
      getThreadById,
      addUnreadThread,
      navigate,
    ],
  );

  return { startStreamingResponse };
}
