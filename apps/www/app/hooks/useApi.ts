import { useState, useCallback } from 'react';
import { AxiosError } from 'axios';
import { apiClient } from '~/utils/axios-client';

export function useApi() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const get = useCallback(async <T>(url: string): Promise<T | null> => {
    setLoading(true);
    setError(null);
    try {
      const data = await apiClient.get<T>(url);
      return data;
    } catch (err: unknown) {
      if (err instanceof AxiosError) {
        setError(err.response?.data?.message || 'Request failed');
      } else {
        setError('Request failed');
      }
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const post = useCallback(
    async <T, U = Record<string, unknown>>(
      url: string,
      data?: U,
    ): Promise<T | null> => {
      setLoading(true);
      setError(null);
      try {
        const response = await apiClient.post<T>(url, data);
        return response;
      } catch (err: unknown) {
        if (err instanceof AxiosError) {
          setError(err.response?.data?.message || 'Request failed');
        } else {
          setError('Request failed');
        }
        return null;
      } finally {
        setLoading(false);
      }
    },
    [],
  );

  const put = useCallback(
    async <T, U = Record<string, unknown>>(
      url: string,
      data?: U,
    ): Promise<T | null> => {
      setLoading(true);
      setError(null);
      try {
        const response = await apiClient.put<T>(url, data);
        return response;
      } catch (err: unknown) {
        if (err instanceof AxiosError) {
          setError(err.response?.data?.message || 'Request failed');
        } else {
          setError('Request failed');
        }
        return null;
      } finally {
        setLoading(false);
      }
    },
    [],
  );

  const del = useCallback(async <T>(url: string): Promise<T | null> => {
    setLoading(true);
    setError(null);
    try {
      const response = await apiClient.delete<T>(url);
      return response;
    } catch (err: unknown) {
      if (err instanceof AxiosError) {
        setError(err.response?.data?.message || 'Request failed');
      } else {
        setError('Request failed');
      }
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  return { get, post, put, delete: del, loading, error };
}
