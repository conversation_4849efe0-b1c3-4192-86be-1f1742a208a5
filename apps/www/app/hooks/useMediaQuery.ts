import * as React from 'react';

/**
 * A custom hook to efficiently track media query matches.
 * @param query The media query string to watch.
 * @returns `true` if the query matches, otherwise `false`.
 */
export function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = React.useState(false);

  React.useEffect(() => {
    // This effect runs only on the client
    if (typeof window !== 'undefined') {
      const media = window.matchMedia(query);
      if (media.matches !== matches) {
        setMatches(media.matches);
      }
      const listener = () => setMatches(media.matches);
      // Use the modern `addEventListener` method
      media.addEventListener('change', listener);
      // Cleanup listener on component unmount
      return () => media.removeEventListener('change', listener);
    }
  }, [matches, query]);

  return matches;
}
