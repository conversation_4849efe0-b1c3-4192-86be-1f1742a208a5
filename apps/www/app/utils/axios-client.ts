import axios, { type AxiosInstance, type AxiosError } from 'axios';
import { useChatStore } from '~/store/chatStore';

interface ApiError {
  message: string;
  statusCode: number;
}

interface ApiErrorEventDetail {
  message: string;
  description: string;
  action?: {
    label: string;
    onClick: () => void;
  };
}

class AxiosApiClient {
  private instance: AxiosInstance;
  private refreshPromise: Promise<string | null> | null = null;

  constructor() {
    this.instance = axios.create({
      baseURL: '/',
      withCredentials: true,
      timeout: 10000,
    });

    this.setupInterceptors();
  }

  /**
   * Dispatches a global custom event for API errors to be caught by the UI.
   */
  private dispatchErrorEvent(detail: ApiErrorEventDetail) {
    const event = new CustomEvent<ApiErrorEventDetail>('apiError', {
      detail,
    });
    window.dispatchEvent(event);
  }

  /**
   * Translates an AxiosError into a user-friendly notification.
   */
  private handleApiError(error: AxiosError<ApiError>) {
    const status = error.response?.data?.statusCode || error.response?.status;
    const message =
      error.response?.data?.message || 'An unknown error occurred.';

    switch (status) {
      case 401:
        // Get the current input value from the store and save it if it exists.
        const unsentMessage = useChatStore.getState().inputValue;
        if (unsentMessage && unsentMessage.trim()) {
          localStorage.setItem('unsentChatMessage', unsentMessage);
        }

        this.dispatchErrorEvent({
          message: 'Authentication required',
          description: 'Your session may have expired. Please log in again.',
          action: {
            label: 'Login',
            onClick: () => (window.location.href = '/api/auth/google'),
          },
        });
        break;
      case 429:
        this.dispatchErrorEvent({
          message: 'Rate limit exceeded',
          description:
            'You have made too many requests. Please try again later.',
        });
        break;
      default:
        this.dispatchErrorEvent({
          message: 'An error occurred',
          description: message,
        });
        break;
    }
  }

  /**
   * Sets up response interceptors to handle token refreshes and global errors.
   */
  private setupInterceptors() {
    this.instance.interceptors.response.use(
      (response) => response,
      async (error: AxiosError<ApiError>) => {
        const originalRequest = error.config as {
          _retry?: boolean;
          url?: string;
        };

        const is401Error = error.response?.status === 401;
        const isRefreshEndpoint = originalRequest.url === '/api/auth/refresh';

        // If the 401 was from the refresh endpoint itself, or if this is already a retry, give up.
        if (is401Error && (isRefreshEndpoint || originalRequest._retry)) {
          this.handleApiError(error);
          return Promise.reject(error);
        }

        // If it's any other 401, attempt to refresh the token.
        if (is401Error) {
          originalRequest._retry = true;
          try {
            await this.refreshAccessToken();
            return this.instance(originalRequest);
          } catch (refreshError) {
            // The refreshAccessToken promise rejected, so we can't recover.
            // The handleApiError was already called in the initial 401 check.
            return Promise.reject(refreshError);
          }
        }

        // Handle other errors that are not 401s (e.g., 500, 404).
        this.handleApiError(error);
        return Promise.reject(error);
      },
    );
  }

  /**
   * Manages the token refresh process, ensuring it only runs once at a time.
   */
  private async refreshAccessToken(): Promise<string | null> {
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    this.refreshPromise = this.instance
      .post('/api/auth/refresh')
      .then((response) => response.data.accessToken)
      .catch((error) => {
        // If the refresh itself fails, we reject the promise so the interceptor can handle it.
        return Promise.reject(error);
      })
      .finally(() => {
        this.refreshPromise = null;
      });

    return this.refreshPromise;
  }

  // --- Public API Methods ---

  public async get<T>(url: string, config = {}): Promise<T> {
    const response = await this.instance.get<T>(url, config);
    return response.data;
  }

  public async post<T>(url: string, data?: unknown, config = {}): Promise<T> {
    const response = await this.instance.post<T>(url, data, config);
    return response.data;
  }

  public async put<T>(url: string, data?: unknown, config = {}): Promise<T> {
    const response = await this.instance.put<T>(url, data, config);
    return response.data;
  }

  public async patch<T>(url: string, data?: unknown, config = {}): Promise<T> {
    const response = await this.instance.patch<T>(url, data, config);
    return response.data;
  }

  public async delete<T>(url: string, config = {}): Promise<T> {
    const response = await this.instance.delete<T>(url, config);
    return response.data;
  }
}

export const apiClient = new AxiosApiClient();
