// apps/www/app/auth/auth-provider.tsx (fixed types)
import React, { createContext, useState, useEffect, useCallback } from 'react';
import { apiClient } from '../utils/axios-client';

interface User {
  id: string;
  email: string;
  name: string;
  picture?: string;
}

export interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  isLoading: boolean;
  login: (messageToSave?: string) => void;
  logout: () => void;
  fetchUser: () => Promise<void>;
  refreshTokens: () => Promise<boolean>;
}

export const AuthContext = createContext<AuthContextType | undefined>(
  undefined,
);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const fetchUser = useCallback(async () => {
    setIsLoading(true);
    try {
      const userData = await apiClient.get<User>('/api/auth/profile');
      if (userData) {
        setUser(userData);
        setIsAuthenticated(true);
      } else {
        setUser(null);
        setIsAuthenticated(false);
      }
    } catch (error) {
      console.error('Failed to fetch user:', error);
      setUser(null);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshTokens = useCallback(async (): Promise<boolean> => {
    try {
      await apiClient.post('/api/auth/refresh');
      await fetchUser();
      return true;
    } catch (error) {
      console.error('Token refresh failed:', error);
      return false;
    }
  }, [fetchUser]);

  useEffect(() => {
    fetchUser();
  }, [fetchUser]);

  const login = (messageToSave?: string) => {
    if (messageToSave) {
      localStorage.setItem('unsentChatMessage', messageToSave);
    }
    window.location.href = '/api/auth/google';
  };

  const logout = async () => {
    try {
      await apiClient.post('/api/auth/logout');
    } catch (error) {
      console.error('Logout failed:', error);
    } finally {
      setUser(null);
      setIsAuthenticated(false);
      window.location.reload();
    }
  };

  const value = {
    isAuthenticated,
    user,
    isLoading,
    login,
    logout,
    fetchUser,
    refreshTokens,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}
