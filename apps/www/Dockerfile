FROM node:20-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
COPY apps/www/package.json ./apps/www/
COPY packages/eslint-config/package.json ./packages/eslint-config/
COPY packages/typescript-config/package.json ./packages/typescript-config/
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Build the www app for production
RUN cd apps/www && npm run build

# Use a minimal Caddy image for the final stage
FROM caddy:2-alpine AS runner
WORKDIR /srv

# Copy Caddy configuration and the built static assets
COPY apps/www/Caddyfile /etc/caddy/Caddyfile
COPY --from=builder /app/apps/www/build/client .