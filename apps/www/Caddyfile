{
	# global options
	admin off # theres no need for the admin api in railway's environment
	persist_config off # storage isn't persistent anyway
	auto_https off # railway handles https for us, this would cause issues if left enabled
	log {
		# runtime logs
		format json # set runtime log format to json mode 
	}
	servers {
		# server options
		trusted_proxies static private_ranges # trust railway's proxy
	}
}
:{$PORT} {
    log {
        format json
    }

    # Set the root directory to the static build output
    root * /srv

    # Rewrite all non-file requests to index.html for SPA routing
    try_files {path} /index.html

    # Set appropriate caching headers for static assets
    header {
        Cache-Control "public, max-age=********, immutable"
    }

    # Serve the static files, using pre-compressed assets if available
    file_server {
        precompressed zstd gzip
    }
}
