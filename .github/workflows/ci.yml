name: 🔍 CI - Code Quality & Security

on:
  pull_request:
    branches: [main, develop]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  # =====================================================
  # Code Quality
  # =====================================================
  quality:
    name: 📝 Code Quality
    runs-on: ubuntu-latest
    timeout-minutes: 10

    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - run: npm ci

      - run: npm run typecheck
        name: 🔍 Type checking

      - run: npm run lint
        name: 🧹 Linting

      - run: npm run format:check
        name: 💅 Format checking

  # =====================================================
  # Security Scan
  # =====================================================
  security:
    name: 🔒 Security Scan
    runs-on: ubuntu-latest
    timeout-minutes: 15
    permissions:
      security-events: write
      actions: read
      contents: read
    env:
      SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}

    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - run: npm ci

      - name: 🔍 Run Snyk
        if: env.SNYK_TOKEN != ''
        uses: snyk/actions/node@master
        continue-on-error: true
        with:
          args: --severity-threshold=high
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}

  # =====================================================
  # Build + Health Check
  # =====================================================
  build:
    name: 🏗️ Build & Health Check
    runs-on: ubuntu-latest
    timeout-minutes: 15

    strategy:
      matrix:
        app: [server, www]

    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - run: npm ci

      - name: 🏗️ Build ${{ matrix.app }}
        run: cd apps/${{ matrix.app }} && npm run build

      - name: 📦 Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-${{ matrix.app }}
          path: apps/${{ matrix.app }}/dist
          retention-days: 1

      - name: 🚦 Health Check (server only)
        if: matrix.app == 'server'
        run: |
          node apps/server/dist/main &

          echo "Waiting for server to be ready..."
          for i in {1..10}; do
            curl --fail http://localhost:3001/healthz && break
            sleep 2
          done

  # =====================================================
  # Basic Tests
  # =====================================================
  test:
    name: 🧪 Tests
    runs-on: ubuntu-latest
    timeout-minutes: 10
    if: github.event_name == 'pull_request'

    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - run: npm ci

      - name: 🧪 Run tests
        run: npm run test
        continue-on-error: true

  # =====================================================
  # CI Summary (Always Runs)
  # =====================================================
  ci-success:
    name: ✅ CI Summary
    runs-on: ubuntu-latest
    needs: [quality, security, build]
    if: always()

    steps:
      - name: ✅ All checks passed
        if: ${{ needs.quality.result == 'success' && needs.security.result == 'success' && needs.build.result == 'success' }}
        run: echo "🎉 All CI checks passed!"

      - name: ❌ Some checks failed
        if: ${{ needs.quality.result != 'success' || needs.security.result != 'success' || needs.build.result != 'success' }}
        run: |
          echo "❌ Some CI checks failed:"
          echo "Quality: ${{ needs.quality.result }}"
          echo "Security: ${{ needs.security.result }}"
          echo "Build: ${{ needs.build.result }}"
          exit 1
