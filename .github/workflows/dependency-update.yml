name: 📦 Dependency Updates

on:
  schedule:
    # Run every Monday at 9 AM UTC
    - cron: '0 9 * * 1'
  workflow_dispatch: # Manual trigger

jobs:
  # =====================================================
  # Security Updates
  # =====================================================
  security-updates:
    name: 🔒 Security Updates
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: 🔍 Audit dependencies
        run: |
          echo "🔍 Running security audit..."
          npm audit --audit-level=moderate --json > audit-results.json || true
          VULN_COUNT=$(jq '.metadata.vulnerabilities.total // 0' audit-results.json)
          echo "Found $VULN_COUNT vulnerabilities"
          if [ "$VULN_COUNT" -gt 0 ]; then
            echo "🚨 Security vulnerabilities found!"
            npm audit --audit-level=moderate
            echo "🔧 Attempting automatic fixes..."
            npm audit fix --force || true
            if git diff --quiet package-lock.json; then
              echo "No automatic fixes available"
            else
              echo "✅ Applied automatic security fixes"
              echo "SECURITY_FIXES_APPLIED=true" >> $GITHUB_ENV
            fi
          else
            echo "✅ No security vulnerabilities found"
          fi

      - name: 📝 Create draft security fix PR
        if: env.SECURITY_FIXES_APPLIED == 'true'
        uses: peter-evans/create-pull-request@v5
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: '🔒 fix: apply automatic security fixes'
          title: '🔒 Security: Automatic dependency security fixes'
          body: |
            ## 🔒 Automatic Security Fixes

            This draft PR contains the fixes from `npm audit fix --force`.

            Please review, test, and merge when ready.
          branch: security/auto-fixes
          draft: true
          delete-branch: true
          labels: |
            security
            dependencies
            automated

  # =====================================================
  # Dependency Health Check
  # =====================================================
  dependency-health:
    name: 📊 Dependency Health
    runs-on: ubuntu-latest

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: 📊 Check dependency health
        run: |
          echo "📊 Checking dependency health..."
          npm ci
          echo "🔍 Checking for outdated packages..."
          npm outdated --json > outdated.json || true
          OUTDATED_COUNT=$(jq 'length // 0' outdated.json)
          echo "Found $OUTDATED_COUNT outdated packages"
          if [ "$OUTDATED_COUNT" -gt 0 ]; then
            echo "📦 Outdated packages found:"
            npm outdated
          else
            echo "✅ All packages are up to date"
          fi
          echo "📏 Checking bundle sizes..."
          npm run build || echo "⚠️ Build failed - skipping bundle size check"

      - name: 📝 Create dependency report
        run: |
          echo "## 📊 Dependency Health Report" >> $GITHUB_STEP_SUMMARY
          echo "- **Date**: $(date)" >> $GITHUB_STEP_SUMMARY
          echo "- **Node.js**: $(node --version)" >> $GITHUB_STEP_SUMMARY
          echo "- **npm**: $(npm --version)" >> $GITHUB_STEP_SUMMARY
          OUTDATED_COUNT=$(jq 'length // 0' outdated.json)
          echo "- **Outdated packages**: $OUTDATED_COUNT" >> $GITHUB_STEP_SUMMARY
          npm audit --audit-level=info --json > audit.json || true
          VULN_COUNT=$(jq '.metadata.vulnerabilities.total // 0' audit.json)
          echo "- **Security vulnerabilities**: $VULN_COUNT" >> $GITHUB_STEP_SUMMARY

  # =====================================================
  # License Compliance Check
  # =====================================================
  license-check:
    name: ⚖️ License Compliance
    runs-on: ubuntu-latest

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: ⚖️ Check licenses
        run: |
          echo "⚖️ Checking dependency licenses..."
          npm install -g license-checker
          license-checker --json > licenses.json
          PROBLEMATIC_LICENSES=("GPL-2.0" "GPL-3.0" "AGPL-1.0" "AGPL-3.0")
          echo "🔍 Scanning for problematic licenses..."
          for license in "${PROBLEMATIC_LICENSES[@]}"; do
            if grep -q "$license" licenses.json; then
              echo "⚠️ Found potentially problematic license: $license"
              grep -l "$license" licenses.json
            fi
          done
          echo "✅ License check completed"

      - name: 📝 Upload license report
        uses: actions/upload-artifact@v4
        with:
          name: license-report
          path: licenses.json
          retention-days: 30
